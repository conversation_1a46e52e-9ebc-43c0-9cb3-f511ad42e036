package com.cdkit.modules.cm.infrastructure.adapters.outsourcing;

import com.cdkit.common.api.vo.Result;
import com.cdkit.modules.client.wms.outsourcing.IOutsourcingOrderApi;
import com.cdkit.modules.client.wms.outsourcing.vo.OutsourcingOrderVo;
import com.cdkit.modules.cm.domain.gateway.outsourcing.OutsourcingExternalGateway;
import com.cdkit.modules.cm.domain.outsourcing.entity.OutsourcingOrderEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.Date;

/**
 * 外委外部服务Feign适配器
 * 调用外委服务的Feign接口
 * 
 * <AUTHOR>
 * @date 2025-08-13
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OutsourcingExternalFeignAdapter implements OutsourcingExternalGateway {

    private final IOutsourcingOrderApi outsourcingOrderApi;

    @Override
    public String addOutsourcingOrder(List<OutsourcingOrderEntity> outsourcingOrders) {
        log.info("调用外委服务新增外委单据，物料明细数量: {}", outsourcingOrders.size());

        if (outsourcingOrders == null || outsourcingOrders.isEmpty()) {
            throw new IllegalArgumentException("外委单据物料明细列表不能为空");
        }

        try {

            // 直接转换为WMS VO格式：使用第一个实体作为主要信息来源
            OutsourcingOrderVo wmsOrder = convertToWmsOrderVo(outsourcingOrders);

            // 调用外委服务接口：/wms/api/outsourcingOrder/addOutsourcingOrder
            Result<String> result = outsourcingOrderApi.addOutsourcingOrder(wmsOrder);

            if (result != null && result.isSuccess()) {
                String outsourcingOrderId = result.getResult();
                log.info("调用外委服务新增外委单据成功，外委单据ID: {}, 物料明细数量: {}",
                        outsourcingOrderId, outsourcingOrders.size());
                return outsourcingOrderId;
            } else {
                String errorMsg = result != null ? result.getMessage() : "未知错误";
                log.error("调用外委服务新增外委单据失败: {}", errorMsg);
                throw new RuntimeException("新增外委单据失败: " + errorMsg);
            }

        } catch (Exception e) {
            log.error("调用外委服务新增外委单据失败", e);
            throw new RuntimeException("新增外委单据失败: " + e.getMessage());
        }
    }


    @Override
    public List<OutsourcingOrderEntity> queryBySourceBillNumberAll(String sourceBillNumber) {
        log.info("调用外委服务查询关联外委单，上游单据号: {}", sourceBillNumber);

        try {
            // 调用外委服务接口：/wms/api/outsourcingOrder/querBySourceBillNumberAll
            Result<List<OutsourcingOrderVo>> result = outsourcingOrderApi.querBySourceBillNumberAll(sourceBillNumber);

            if (result != null && result.isSuccess() && result.getResult() != null) {
                // 转换为领域实体格式
                List<OutsourcingOrderEntity> orders = convertToEntityList(result.getResult());
                log.info("调用外委服务查询关联外委单成功，上游单据号: {}, 单据数量: {}",
                        sourceBillNumber, orders.size());
                return orders;
            } else {
                String errorMsg = result != null ? result.getMessage() : "未知错误";
                log.warn("调用外委服务查询关联外委单失败，上游单据号: {}, 错误: {}", sourceBillNumber, errorMsg);
                return Collections.emptyList();
            }

        } catch (Exception e) {
            log.error("调用外委服务查询关联外委单失败，上游单据号: {}", sourceBillNumber, e);
            return Collections.emptyList();
        }
    }

    @Override
    public Map<String, BigDecimal> batchGetTransferredAmountByProducts(String planId, List<String> productNames) {
        log.info("批量查询产品已转量，项目计划ID: {}, 产品数量: {}", planId, productNames.size());

        Map<String, BigDecimal> resultMap = new HashMap<>();

        if (!StringUtils.hasText(planId) || productNames == null || productNames.isEmpty()) {
            return resultMap;
        }

        try {
            // 1. 调用外委服务获取所有关联外委单（主子表全）
            Result<List<OutsourcingOrderVo>> result = outsourcingOrderApi.querBySourceBillNumberAll(planId);

            if (result == null || !result.isSuccess() || result.getResult() == null) {
                log.warn("调用外委服务查询关联外委单失败或无数据，项目计划ID: {}", planId);
                // 确保所有产品都有返回值（即使是0）
                for (String productName : productNames) {
                    resultMap.put(productName, BigDecimal.ZERO);
                }
                return resultMap;
            }

            // 2. 遍历所有外委单据，从物料明细中统计已转量
            for (OutsourcingOrderVo orderVo : result.getResult()) {
                // 获取物料明细列表
                List<?> materialList = getOutsourcingOrderMaterialList(orderVo);
                if (materialList == null || materialList.isEmpty()) {
                    continue;
                }

                // 遍历物料明细，按materialCode统计quantityReceivable
                for (Object materialObj : materialList) {
                    try {
                        String materialCode = getMaterialCode(materialObj);
                        BigDecimal quantityReceivable = getQuantityReceivable(materialObj);

                        // 检查是否是我们要查询的产品（通过materialCode匹配productName）
                        if (StringUtils.hasText(materialCode) && productNames.contains(materialCode) &&
                            quantityReceivable != null && quantityReceivable.compareTo(BigDecimal.ZERO) > 0) {

                            BigDecimal currentAmount = resultMap.getOrDefault(materialCode, BigDecimal.ZERO);
                            resultMap.put(materialCode, currentAmount.add(quantityReceivable));
                        }
                    } catch (Exception e) {
                        log.warn("处理物料明细失败: {}", e.getMessage());
                    }
                }
            }

            // 3. 确保所有产品都有返回值（即使是0）
            for (String productName : productNames) {
                resultMap.putIfAbsent(productName, BigDecimal.ZERO);
            }

            log.info("批量查询产品已转量成功，项目计划ID: {}, 产品数量: {}, 统计结果: {}",
                    planId, productNames.size(), resultMap);

        } catch (Exception e) {
            log.error("批量查询产品已转量失败，项目计划ID: {}, 产品数量: {}", planId, productNames.size(), e);
            // 返回默认值
            for (String productName : productNames) {
                resultMap.put(productName, BigDecimal.ZERO);
            }
        }

        return resultMap;
    }

    @Override
    public Map<String, BigDecimal> batchGetTransferredAmountByMaterials(String planId, List<String> materialCodes) {
        log.info("批量查询物料已转量，项目计划ID: {}, 物料数量: {}", planId, materialCodes.size());

        Map<String, BigDecimal> resultMap = new HashMap<>();

        if (!StringUtils.hasText(planId) || materialCodes == null || materialCodes.isEmpty()) {
            return resultMap;
        }

        try {
            // 1. 调用外委服务获取所有关联外委单（主子表全）
            Result<List<OutsourcingOrderVo>> result = outsourcingOrderApi.querBySourceBillNumberAll(planId);

            if (result == null || !result.isSuccess() || result.getResult() == null) {
                log.warn("调用外委服务查询关联外委单失败或无数据，项目计划ID: {}", planId);
                // 确保所有物料都有返回值（即使是0）
                for (String materialCode : materialCodes) {
                    resultMap.put(materialCode, BigDecimal.ZERO);
                }
                return resultMap;
            }

            // 2. 遍历所有外委单据，从物料明细中统计已转量
            for (OutsourcingOrderVo orderVo : result.getResult()) {
                // 获取物料明细列表
                List<?> materialList = getOutsourcingOrderMaterialList(orderVo);
                if (materialList == null || materialList.isEmpty()) {
                    continue;
                }

                // 遍历物料明细，按materialCode统计quantityReceivable
                for (Object materialObj : materialList) {
                    try {
                        String materialCode = getMaterialCode(materialObj);
                        BigDecimal quantityReceivable = getQuantityReceivable(materialObj);

                        // 检查是否是我们要查询的物料编码
                        if (StringUtils.hasText(materialCode) && materialCodes.contains(materialCode) &&
                            quantityReceivable != null && quantityReceivable.compareTo(BigDecimal.ZERO) > 0) {

                            BigDecimal currentAmount = resultMap.getOrDefault(materialCode, BigDecimal.ZERO);
                            resultMap.put(materialCode, currentAmount.add(quantityReceivable));
                        }
                    } catch (Exception e) {
                        log.warn("处理物料明细失败: {}", e.getMessage());
                    }
                }
            }

            // 3. 确保所有物料都有返回值（即使是0）
            for (String materialCode : materialCodes) {
                resultMap.putIfAbsent(materialCode, BigDecimal.ZERO);
            }

            log.info("批量查询物料已转量成功，项目计划ID: {}, 物料数量: {}, 统计结果: {}",
                    planId, materialCodes.size(), resultMap);

        } catch (Exception e) {
            log.error("批量查询物料已转量失败，项目计划ID: {}, 物料数量: {}", planId, materialCodes.size(), e);
            // 返回默认值
            for (String materialCode : materialCodes) {
                resultMap.put(materialCode, BigDecimal.ZERO);
            }
        }

        return resultMap;
    }



    @Override
    public OutsourcingOrderEntity checkOutsourcingOrderStatus(String outsourcingOrderId) {
        log.info("检查外委单据状态，外委单据ID: {}", outsourcingOrderId);

        try {
            // TODO: 调用外委服务接口查询单据状态
            // 可能需要调用单独的状态查询接口
            
            // 暂时返回null，等外委服务提供后再实现
            log.info("检查外委单据状态成功，外委单据ID: {}", outsourcingOrderId);
            return null;
            
        } catch (Exception e) {
            log.error("检查外委单据状态失败，外委单据ID: {}", outsourcingOrderId, e);
            return null;
        }
    }

    @Override
    public boolean cancelOutsourcingOrder(String outsourcingOrderId) {
        log.info("取消外委单据，外委单据ID: {}", outsourcingOrderId);

        try {
            // TODO: 调用外委服务接口取消单据
            // 可能需要调用单独的取消接口
            
            // 暂时返回true，等外委服务提供后再实现
            log.info("取消外委单据成功，外委单据ID: {}", outsourcingOrderId);
            return true;
            
        } catch (Exception e) {
            log.error("取消外委单据失败，外委单据ID: {}", outsourcingOrderId, e);
            return false;
        }
    }

    /**
     * 将领域实体列表转换为WMS外委单据VO
     * 根据Controller入参的嵌套结构，将产品和其子物料都添加到物料明细列表中
     *
     * @param entities 领域实体列表（包含产品和子物料的嵌套结构）
     * @return WMS外委单据VO
     */
    private OutsourcingOrderVo convertToWmsOrderVo(List<OutsourcingOrderEntity> entities) {
        if (entities == null || entities.isEmpty()) {
            return null;
        }

        // 使用第一个实体作为主要信息来源
        OutsourcingOrderEntity firstEntity = entities.get(0);
        OutsourcingOrderVo orderVo = new OutsourcingOrderVo();

        // 使用BeanUtils复制可用的字段
        BeanUtils.copyProperties(firstEntity, orderVo);

        // 设置来源单据号：从项目计划编码获取
        if (StringUtils.hasText(firstEntity.getPlanCode())) {
            orderVo.setSourceBillNumber(firstEntity.getPlanCode());
        } else if (StringUtils.hasText(firstEntity.getUpstreamOrderCode())) {
            // 如果planCode为空，使用upstreamOrderCode作为备选
            orderVo.setSourceBillNumber(firstEntity.getUpstreamOrderCode());
        }

        // 创建物料明细列表：包含产品和所有子物料
        List<Object> materialList = new ArrayList<>();

        for (OutsourcingOrderEntity entity : entities) {
            // 只处理有外委量的物料（产品或半成品）
            if (entity.getOutsourcingAmount() != null && entity.getOutsourcingAmount().compareTo(BigDecimal.ZERO) > 0) {
                Object materialVo = convertToMaterialVo(entity);
                materialList.add(materialVo);
            }
        }

        // 设置物料明细列表（使用反射设置，因为我们不确定具体的字段名）
        try {
            // 尝试设置 outsourcingOrderMaterialList 字段
            java.lang.reflect.Method setMaterialListMethod = orderVo.getClass().getMethod("setOutsourcingOrderMaterialList", List.class);
            setMaterialListMethod.invoke(orderVo, materialList);
        } catch (Exception e) {
            log.warn("设置物料明细列表失败，可能字段名不匹配: {}", e.getMessage());
        }

        // 设置默认值
        if (orderVo.getCreateTime() == null) {
            orderVo.setCreateTime(new Date());
        }
        if (!StringUtils.hasText(orderVo.getCreateBy())) {
            orderVo.setCreateBy("system");
        }

        return orderVo;
    }

    /**
     * 将WMS VO列表转换为领域实体列表
     *
     * @param vos WMS VO列表
     * @return 领域实体列表
     */
    private List<OutsourcingOrderEntity> convertToEntityList(List<OutsourcingOrderVo> vos) {
        if (vos == null || vos.isEmpty()) {
            return Collections.emptyList();
        }

        return vos.stream()
                .map(this::convertToEntity)
                .collect(Collectors.toList());
    }

    /**
     * 将WMS VO转换为领域实体
     *
     * @param vo WMS VO
     * @return 领域实体
     */
    private OutsourcingOrderEntity convertToEntity(OutsourcingOrderVo vo) {
        if (vo == null) {
            return null;
        }

        OutsourcingOrderEntity entity = new OutsourcingOrderEntity();
        BeanUtils.copyProperties(vo, entity);
        return entity;
    }

    /**
     * 将领域实体转换为WMS物料明细VO
     * 创建一个通用的物料明细对象，包含WMS接口需要的字段
     *
     * @param entity 领域实体
     * @return WMS物料明细对象
     */
    private Object convertToMaterialVo(OutsourcingOrderEntity entity) {
        // 创建一个Map来存储物料明细信息，因为我们不确定具体的VO类结构
        Map<String, Object> materialMap = new HashMap<>();

        // 基本信息
        materialMap.put("materialCode", entity.getMaterialCode());
        materialMap.put("materialName", entity.getProductName()); // 使用产品名称作为物料名称
        materialMap.put("itemType", entity.getItemType());
        materialMap.put("quantityReceivable", entity.getOutsourcingAmount()); // 外委量作为应收数量

        // 系统字段
        materialMap.put("createBy", entity.getCreateBy() != null ? entity.getCreateBy() : "system");
        materialMap.put("createTime", entity.getCreateTime() != null ? entity.getCreateTime() : new Date());
        materialMap.put("updateBy", entity.getUpdateBy());
        materialMap.put("updateTime", entity.getUpdateTime());
        materialMap.put("tenantId", entity.getTenantId() != null ? entity.getTenantId() : 1005);
        materialMap.put("delFlag", entity.getDelFlag() != null ? entity.getDelFlag() : 0);

        // 物料相关默认值
        materialMap.put("enable", 1); // 启用
        materialMap.put("calculate", 0); // 不计算
        materialMap.put("qualityTesting", 0); // 不质检
        materialMap.put("enableBatchNumber", 1); // 启用批次号
        materialMap.put("enableExpiration", "0"); // 不启用有效期
        materialMap.put("enablePiecesNumber", "0"); // 不启用件数
        materialMap.put("operateType", "purchasable"); // 可购买

        // 字典文本
        materialMap.put("itemType_dictText", getItemTypeDictText(entity.getItemType()));
        materialMap.put("enable_dictText", "启用");
        materialMap.put("operateType_dictText", "可购买");
        materialMap.put("enablePiecesNumber_dictText", "否");

        return materialMap;
    }

    /**
     * 获取物料类型字典文本
     */
    private String getItemTypeDictText(String itemType) {
        if ("PRODUCT".equals(itemType)) {
            return "产品";
        } else if ("SEMI".equals(itemType)) {
            return "半成品";
        } else if ("REAGENT_MATERIALS".equals(itemType)) {
            return "试剂材料";
        }
        return itemType;
    }

    /**
     * 通过反射获取OutsourcingOrderVo的outsourcingOrderMaterialList字段
     */
    private List<?> getOutsourcingOrderMaterialList(OutsourcingOrderVo orderVo) {
        try {
            java.lang.reflect.Method getMethod = orderVo.getClass().getMethod("getOutsourcingOrderMaterialList");
            Object result = getMethod.invoke(orderVo);
            return result instanceof List ? (List<?>) result : null;
        } catch (Exception e) {
            log.debug("获取outsourcingOrderMaterialList失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 通过反射获取物料明细对象的materialCode字段
     */
    private String getMaterialCode(Object materialObj) {
        try {
            java.lang.reflect.Method getMethod = materialObj.getClass().getMethod("getMaterialCode");
            Object result = getMethod.invoke(materialObj);
            return result instanceof String ? (String) result : null;
        } catch (Exception e) {
            log.debug("获取materialCode失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 通过反射获取物料明细对象的quantityReceivable字段
     */
    private BigDecimal getQuantityReceivable(Object materialObj) {
        try {
            java.lang.reflect.Method getMethod = materialObj.getClass().getMethod("getQuantityReceivable");
            Object result = getMethod.invoke(materialObj);
            if (result instanceof BigDecimal) {
                return (BigDecimal) result;
            } else if (result instanceof Number) {
                return new BigDecimal(result.toString());
            }
            return null;
        } catch (Exception e) {
            log.debug("获取quantityReceivable失败: {}", e.getMessage());
            return null;
        }
    }
}
