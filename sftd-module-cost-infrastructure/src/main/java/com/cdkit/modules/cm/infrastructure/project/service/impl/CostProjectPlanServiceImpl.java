package com.cdkit.modules.cm.infrastructure.project.service.impl;


import com.cdkit.modules.cm.infrastructure.project.entity.*;
import com.cdkit.modules.cm.infrastructure.project.mapper.*;
import com.cdkit.modules.cm.infrastructure.project.service.ICostProjectPlanService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;
import java.util.Collection;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * @Description: 项目计划
 * @Author: cdkit-boot
 * @Date:   2025-07-18
 * @Version: V1.0
 */
@Service
public class CostProjectPlanServiceImpl extends ServiceImpl<CostProjectPlanMapper, CostProjectPlan> implements ICostProjectPlanService {

	@Autowired
	private CostProjectPlanMapper costProjectPlanMapper;
	@Autowired
	private CostProjectPlanDetailMapper costProjectPlanDetailMapper;
	@Autowired
	private CostDirectCostMapper costDirectCostMapper;
	@Autowired
	private CostOtherCostMapper costOtherCostMapper;
	@Autowired
	private CostTaxCostMapper costTaxCostMapper;
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveMain(CostProjectPlan costProjectPlan, List<CostProjectPlanDetail> costProjectPlanDetailList, List<CostDirectCost> costDirectCostList, List<CostOtherCost> costOtherCostList, List<CostTaxCost> costTaxCostList) {
		costProjectPlanMapper.insert(costProjectPlan);
		if(costProjectPlanDetailList!=null && costProjectPlanDetailList.size()>0) {
			for(CostProjectPlanDetail entity:costProjectPlanDetailList) {
				//外键设置
				entity.setPlanId(costProjectPlan.getId());
				costProjectPlanDetailMapper.insert(entity);
			}
		}
		if(costDirectCostList!=null && costDirectCostList.size()>0) {
			for(CostDirectCost entity:costDirectCostList) {
				//外键设置
				entity.setPlanId(costProjectPlan.getId());
				costDirectCostMapper.insert(entity);
			}
		}
		if(costOtherCostList!=null && costOtherCostList.size()>0) {
			for(CostOtherCost entity:costOtherCostList) {
				//外键设置
				entity.setPlanId(costProjectPlan.getId());
				costOtherCostMapper.insert(entity);
			}
		}
		if(costTaxCostList!=null && costTaxCostList.size()>0) {
			for(CostTaxCost entity:costTaxCostList) {
				//外键设置
				entity.setPlanId(costProjectPlan.getId());
				costTaxCostMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateMain(CostProjectPlan costProjectPlan,List<CostProjectPlanDetail> costProjectPlanDetailList,List<CostDirectCost> costDirectCostList,List<CostOtherCost> costOtherCostList,List<CostTaxCost> costTaxCostList) {
		costProjectPlanMapper.updateById(costProjectPlan);
		
		//1.先删除子表数据
		costProjectPlanDetailMapper.deleteByMainId(costProjectPlan.getId());
		costDirectCostMapper.deleteByMainId(costProjectPlan.getId());
		costOtherCostMapper.deleteByMainId(costProjectPlan.getId());
		costTaxCostMapper.deleteByMainId(costProjectPlan.getId());
		
		//2.子表数据重新插入
		if(costProjectPlanDetailList!=null && costProjectPlanDetailList.size()>0) {
			for(CostProjectPlanDetail entity:costProjectPlanDetailList) {
				//外键设置
				entity.setPlanId(costProjectPlan.getId());
				costProjectPlanDetailMapper.insert(entity);
			}
		}
		if(costDirectCostList!=null && costDirectCostList.size()>0) {
			for(CostDirectCost entity:costDirectCostList) {
				//外键设置
				entity.setPlanId(costProjectPlan.getId());
				costDirectCostMapper.insert(entity);
			}
		}
		if(costOtherCostList!=null && costOtherCostList.size()>0) {
			for(CostOtherCost entity:costOtherCostList) {
				//外键设置
				entity.setPlanId(costProjectPlan.getId());
				costOtherCostMapper.insert(entity);
			}
		}
		if(costTaxCostList!=null && costTaxCostList.size()>0) {
			for(CostTaxCost entity:costTaxCostList) {
				//外键设置
				entity.setPlanId(costProjectPlan.getId());
				costTaxCostMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delMain(String id) {
		costProjectPlanDetailMapper.deleteByMainId(id);
		costDirectCostMapper.deleteByMainId(id);
		costOtherCostMapper.deleteByMainId(id);
		costTaxCostMapper.deleteByMainId(id);
		costProjectPlanMapper.deleteById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			costProjectPlanDetailMapper.deleteByMainId(id.toString());
			costDirectCostMapper.deleteByMainId(id.toString());
			costOtherCostMapper.deleteByMainId(id.toString());
			costTaxCostMapper.deleteByMainId(id.toString());
			costProjectPlanMapper.deleteById(id);
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public CostProjectPlan calculateBudgetBasis(String planId) {
		// 获取项目计划主表数据
		CostProjectPlan costProjectPlan = costProjectPlanMapper.selectById(planId);
		if (costProjectPlan == null) {
			throw new RuntimeException("项目计划不存在");
		}

		// 获取项目计划明细数据
		List<CostProjectPlanDetail> detailList = costProjectPlanDetailMapper.selectByMainId(planId);

		// 计算明细数据
		detailList = calculatePlanDetails(detailList);

		// 更新明细数据
		for (CostProjectPlanDetail detail : detailList) {
			costProjectPlanDetailMapper.updateById(detail);
		}

		// 获取直接成本、其他成本、税金及附加数据
		List<CostDirectCost> directCostList = costDirectCostMapper.selectByMainId(planId);
		List<CostOtherCost> otherCostList = costOtherCostMapper.selectByMainId(planId);
		List<CostTaxCost> taxCostList = costTaxCostMapper.selectByMainId(planId);

		// 计算汇总数据
		calculateSummaryData(costProjectPlan, detailList, directCostList, otherCostList, taxCostList);

		// 更新主表数据
		costProjectPlanMapper.updateById(costProjectPlan);

		return costProjectPlan;
	}

	@Override
	public List<CostProjectPlanDetail> calculatePlanDetails(List<CostProjectPlanDetail> costProjectPlanDetailList) {
		if (costProjectPlanDetailList == null || costProjectPlanDetailList.isEmpty()) {
			return costProjectPlanDetailList;
		}

		for (CostProjectPlanDetail detail : costProjectPlanDetailList) {
			// 计算年度预算需求吨 = 密度 × 用量
			if (detail.getDensity() != null && detail.getUsageAmount() != null) {
				BigDecimal demandTon = detail.getDensity().multiply(detail.getUsageAmount())
					.setScale(4, RoundingMode.HALF_UP);
				detail.setDemandTon(demandTon);
			}

			// 计算年度预算应收(油，万元) = 预计年处理量(油，方) × 费率 ÷ 10000
			if (detail.getEstimatedAnnualOil() != null && detail.getFeeRate() != null) {
				BigDecimal revenueOil = detail.getEstimatedAnnualOil()
					.multiply(detail.getFeeRate())
					.divide(new BigDecimal("10000"), 6, RoundingMode.HALF_UP);
				detail.setRevenueOil(revenueOil);
			}

			// 计算年度预算应收(水，万元) = 预计年处理量(水，方) × 费率 ÷ 10000
			if (detail.getEstimatedAnnualWater() != null && detail.getFeeRate() != null) {
				BigDecimal revenueWater = detail.getEstimatedAnnualWater()
					.multiply(detail.getFeeRate())
					.divide(new BigDecimal("10000"), 6, RoundingMode.HALF_UP);
				detail.setRevenueWater(revenueWater);
			}
		}

		return costProjectPlanDetailList;
	}

	/**
	 * 计算汇总数据
	 *
	 * @param costProjectPlan 项目计划主表
	 * @param detailList 明细列表
	 * @param directCostList 直接成本列表
	 * @param otherCostList 其他成本列表
	 * @param taxCostList 税金及附加列表
	 */
	private void calculateSummaryData(CostProjectPlan costProjectPlan,
									  List<CostProjectPlanDetail> detailList,
									  List<CostDirectCost> directCostList,
									  List<CostOtherCost> otherCostList,
									  List<CostTaxCost> taxCostList) {

		// 计算直接成本小计 - 所有AP-10产品型号的年度预算需求吨求和
		BigDecimal directCostTotal = BigDecimal.ZERO;
		if (detailList != null && !detailList.isEmpty()) {
			for (CostProjectPlanDetail detail : detailList) {
				if ("AP-10".equals(detail.getProductModel()) && detail.getDemandTon() != null) {
					directCostTotal = directCostTotal.add(detail.getDemandTon());
				}
			}
		}

		// 如果有直接成本明细，使用明细中的总价
		if (directCostList != null && !directCostList.isEmpty()) {
			BigDecimal directCostFromDetails = BigDecimal.ZERO;
			for (CostDirectCost directCost : directCostList) {
				if (directCost.getTotalExcludingTax() != null) {
					directCostFromDetails = directCostFromDetails.add(directCost.getTotalExcludingTax());
				}
			}
			if (directCostFromDetails.compareTo(BigDecimal.ZERO) > 0) {
				directCostTotal = directCostFromDetails;
			}
		}

		costProjectPlan.setDirectCostTotal(directCostTotal.setScale(2, RoundingMode.HALF_UP));

		// 计算其他成本小计
		BigDecimal otherCostTotal = BigDecimal.ZERO;
		if (otherCostList != null && !otherCostList.isEmpty()) {
			for (CostOtherCost otherCost : otherCostList) {
				if (otherCost.getFeeAmount() != null) {
					otherCostTotal = otherCostTotal.add(otherCost.getFeeAmount());
				}
			}
		}
		costProjectPlan.setOtherCostTotal(otherCostTotal.setScale(2, RoundingMode.HALF_UP));

		// 计算税金及附加小计
		BigDecimal taxCostTotal = BigDecimal.ZERO;
		if (taxCostList != null && !taxCostList.isEmpty()) {
			for (CostTaxCost taxCost : taxCostList) {
				if (taxCost.getFeeAmount() != null) {
					taxCostTotal = taxCostTotal.add(taxCost.getFeeAmount());
				}
			}
		}
		costProjectPlan.setTaxCostTotal(taxCostTotal.setScale(2, RoundingMode.HALF_UP));

		// 计算成本总计 = 直接成本小计 + 其他成本小计 + 税金及附加小计
		BigDecimal costTotal = directCostTotal.add(otherCostTotal).add(taxCostTotal);
		costProjectPlan.setCostTotal(costTotal.setScale(2, RoundingMode.HALF_UP));

		// 计算年度预算应收总计（油+水）
		BigDecimal totalRevenue = BigDecimal.ZERO;
		if (detailList != null && !detailList.isEmpty()) {
			for (CostProjectPlanDetail detail : detailList) {
				if (detail.getRevenueOil() != null) {
					totalRevenue = totalRevenue.add(detail.getRevenueOil());
				}
				if (detail.getRevenueWater() != null) {
					totalRevenue = totalRevenue.add(detail.getRevenueWater());
				}
			}
		}

		// 计算项目利润 = 年度预算应收总计 - 成本总计
		BigDecimal projectProfit = totalRevenue.subtract(costTotal);
		costProjectPlan.setProjectProfit(projectProfit.setScale(2, RoundingMode.HALF_UP));

		// 计算利润率 = 项目利润 / 成本总计
		if (costTotal.compareTo(BigDecimal.ZERO) != 0) {
			BigDecimal profitMargin = projectProfit.divide(costTotal, 4, RoundingMode.HALF_UP)
				.multiply(new BigDecimal("100"));
			costProjectPlan.setProfitMargin(profitMargin.setScale(2, RoundingMode.HALF_UP));
		} else {
			costProjectPlan.setProfitMargin(BigDecimal.ZERO);
		}
	}

}