package com.cdkit.modules.cm.infrastructure.project.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.cm.infrastructure.project.entity.*;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 项目计划
 * @Author: cdkit-boot
 * @Date:   2025-07-18
 * @Version: V1.0
 */
public interface ICostProjectPlanService extends IService<CostProjectPlan> {

	/**
	 * 添加一对多
	 *
	 * @param costProjectPlan
	 * @param costProjectPlanDetailList
	 * @param costDirectCostList
	 * @param costOtherCostList
	 * @param costTaxCostList
	 */
	public void saveMain(CostProjectPlan costProjectPlan, List<CostProjectPlanDetail> costProjectPlanDetailList, List<CostDirectCost> costDirectCostList, List<CostOtherCost> costOtherCostList, List<CostTaxCost> costTaxCostList) ;

	/**
	 * 修改一对多
	 *
   * @param costProjectPlan
   * @param costProjectPlanDetailList
   * @param costDirectCostList
   * @param costOtherCostList
   * @param costTaxCostList
	 */
	public void updateMain(CostProjectPlan costProjectPlan,List<CostProjectPlanDetail> costProjectPlanDetailList,List<CostDirectCost> costDirectCostList,List<CostOtherCost> costOtherCostList,List<CostTaxCost> costTaxCostList);

	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain (String id);

	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);

	/**
	 * 计算项目计划预算依据
	 *
	 * @param planId 计划ID
	 * @return CostProjectPlan 计算后的项目计划
	 */
	public CostProjectPlan calculateBudgetBasis(String planId);

	/**
	 * 计算项目计划明细的预算数据
	 *
	 * @param costProjectPlanDetailList 项目计划明细列表
	 * @return List<CostProjectPlanDetail> 计算后的明细列表
	 */
	public List<CostProjectPlanDetail> calculatePlanDetails(List<CostProjectPlanDetail> costProjectPlanDetailList);

}
