package com.cdkit.modules.cm.domain.project.mode.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.cdkitframework.poi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 直接成本明细
 * @Author: cdkit-boot
 * @Date:   2025-07-18
 * @Version: V1.0
 */
@Schema(description="cost_direct_cost对象")
@Data
public class CostDirectCostEntity implements Serializable {
    private static final long serialVersionUID = 1L;

	/**UUID主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "UUID主键")
    private String id;
	/**关联计划ID*/
    @Schema(description = "关联计划ID")
    private String planId;
	/**产品名称*/
	@Excel(name = "产品名称", width = 15)
    @Schema(description = "产品名称")
    private String productName;
    /**物料名称*/
    @Excel(name = "物料名称", width = 15)
    @Schema(description = "物料名称")
    private String materialName;
	/**预计用量(吨)*/
	@Excel(name = "预计用量(吨)", width = 15)
    @Schema(description = "预计用量(吨)")
    private java.math.BigDecimal estimatedUsage;
	/**配方名称*/
	@Excel(name = "配方名称", width = 15)
    @Schema(description = "配方名称")
    private String formulaName;
	/**配方编号*/
	@Excel(name = "配方编号", width = 15)
    @Schema(description = "配方编号")
    private String formulaCode;
	/**材料成本含税单价(万元)*/
	@Excel(name = "材料成本含税单价(万元)", width = 15)
    @Schema(description = "材料成本含税单价(万元)")
    private java.math.BigDecimal unitPriceIncludingTax;
	/**材料成本不含税单价(万元)*/
	@Excel(name = "材料成本不含税单价(万元)", width = 15)
    @Schema(description = "材料成本不含税单价(万元)")
    private java.math.BigDecimal unitPriceExcludingTax;
	/**税率(%)*/
	@Excel(name = "税率(%)", width = 15)
    @Schema(description = "税率(%)")
    private java.math.BigDecimal taxRate;
	/**材料成本含税总价(万元)*/
	@Excel(name = "材料成本含税总价(万元)", width = 15)
    @Schema(description = "材料成本含税总价(万元)")
    private java.math.BigDecimal totalIncludingTax;
	/**材料成本不含税总价(万元)*/
	@Excel(name = "材料成本不含税总价(万元)", width = 15)
    @Schema(description = "材料成本不含税总价(万元)")
    private java.math.BigDecimal totalExcludingTax;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private Date updateTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**租户ID*/
	@Excel(name = "租户ID", width = 15)
    @Schema(description = "租户ID")
    private Integer tenantId;
	/**删除标识 0:未删除 1:删除*/
	@Excel(name = "删除标识 0:未删除 1:删除", width = 15)
    @Schema(description = "删除标识 0:未删除 1:删除")
    @TableLogic
    private Integer delFlag;
	/**所属部门代码*/
    @Schema(description = "所属部门代码")
    private String sysOrgCode;

    /**
     * 计算含税总价
     * 含税总价 = 预计用量 × 含税单价
     */
    public void calculateTotalIncludingTax() {
        if (estimatedUsage != null && unitPriceIncludingTax != null) {
            this.totalIncludingTax = estimatedUsage.multiply(unitPriceIncludingTax)
                .setScale(2, java.math.RoundingMode.HALF_UP);
        }
    }

    /**
     * 计算不含税总价
     * 不含税总价 = 预计用量 × 不含税单价
     */
    public void calculateTotalExcludingTax() {
        if (estimatedUsage != null && unitPriceExcludingTax != null) {
            this.totalExcludingTax = estimatedUsage.multiply(unitPriceExcludingTax)
                .setScale(2, java.math.RoundingMode.HALF_UP);
        }
    }

    /**
     * 根据含税单价和税率计算不含税单价
     * 不含税单价 = 含税单价 ÷ (1 + 税率)
     */
    public void calculateUnitPriceExcludingTax() {
        if (unitPriceIncludingTax != null && taxRate != null) {
            java.math.BigDecimal divisor = java.math.BigDecimal.ONE.add(taxRate.divide(new java.math.BigDecimal("100")));
            this.unitPriceExcludingTax = unitPriceIncludingTax.divide(divisor, 4, java.math.RoundingMode.HALF_UP);
        }
    }

    /**
     * 计算所有相关价格
     */
    public void calculateAllPrices() {
        calculateUnitPriceExcludingTax();
        calculateTotalIncludingTax();
        calculateTotalExcludingTax();
    }

    /**
     * 判断是否为有效的成本项
     */
    public boolean isValid() {
        return productName != null && !productName.trim().isEmpty()
            && estimatedUsage != null && estimatedUsage.compareTo(java.math.BigDecimal.ZERO) > 0
            && unitPriceIncludingTax != null && unitPriceIncludingTax.compareTo(java.math.BigDecimal.ZERO) > 0;
    }
}
