package com.cdkit.modules.cm.performance.outsourcing;

import com.cdkit.common.api.vo.Result;
import com.cdkit.modules.cm.api.outsourcing.IOutsourcingTransferApi;
import com.cdkit.modules.cm.api.outsourcing.dto.OutsourcingTransferListDTO;
import com.cdkit.modules.cm.api.outsourcing.dto.OutsourcingTransferItemDTO;
import com.cdkit.modules.cm.api.outsourcing.dto.OutsourcingTransferConfirmRequest;
import com.cdkit.modules.cm.application.outsourcing.OutsourcingTransferApplicationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 转外委控制器
 * 
 * <AUTHOR>
 * @date 2025-08-13
 */
@RestController
@RequestMapping("/cm/costProjectPlan/outsourcing")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "转外委管理")
public class OutsourcingTransferController implements IOutsourcingTransferApi {

    private final OutsourcingTransferApplicationService outsourcingTransferApplicationService;

    /**
     * 获取转外委列表
     */
    @Override
    @GetMapping("/list/{planId}")
    @Operation(summary = "获取转外委列表", description = "根据项目计划ID获取可转外委的产品和半成品列表，支持子母表结构")
    public List<OutsourcingTransferItemDTO> getOutsourcingTransferList(
            @Parameter(description = "项目计划ID", required = true) @PathVariable String planId) {

        log.info("获取转外委列表，项目计划ID: {}", planId);

        try {
            if (!StringUtils.hasText(planId)) {
                throw new IllegalArgumentException("项目计划ID不能为空");
            }

            List<OutsourcingTransferItemDTO> items = outsourcingTransferApplicationService.getOutsourcingTransferList(planId);

            log.info("获取转外委列表成功，项目计划ID: {}, 项目数量: {}", planId, items.size());
            return items;

        } catch (Exception e) {
            log.error("获取转外委列表失败，项目计划ID: {}", planId, e);
            throw new RuntimeException("获取转外委列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 确认转外委
     */
    @Override
    @PostMapping("/confirm")
    @Operation(summary = "确认转外委", description = "根据用户填写的外委量生成外委单据")
    public Result<String> confirmOutsourcingTransfer(
            @Parameter(description = "转外委项目列表", required = true) @Valid @RequestBody List<OutsourcingTransferItemDTO> items) {

        log.info("确认转外委，项目数量: {}", items.size());

        try {
            String outsourcingOrderId = outsourcingTransferApplicationService.confirmOutsourcingTransfer(items);
            return Result.OK(outsourcingOrderId);

        } catch (Exception e) {
            log.error("确认转外委失败，项目数量: {}", items.size(), e);
            return Result.error("确认转外委失败: " + e.getMessage());
        }
    }

    /**
     * 根据外委产品更新转外委数据
     */
    @Override
    @PostMapping("/update-by-outsourcing-product")
    @Operation(summary = "根据外委产品更新转外委数据", description = "当用户选择外委产品后，根据外委产品的配方信息更新半成品用量等数据")
    public Result<OutsourcingTransferItemDTO> updateByOutsourcingProduct(
            @Parameter(description = "转外委项目数据", required = true) @RequestBody @Valid OutsourcingTransferItemDTO itemDTO) {

        log.info("根据外委产品更新转外委数据，产品名称: {}, 外委产品: {}", itemDTO.getProductName(), itemDTO.getOutsourcingProduct());

        try {
            if (itemDTO == null) {
                return Result.error("转外委项目数据不能为空");
            }

            if (!StringUtils.hasText(itemDTO.getOutsourcingProduct())) {
                return Result.error("外委产品不能为空");
            }

            OutsourcingTransferItemDTO updatedItem = outsourcingTransferApplicationService.updateByOutsourcingProduct(itemDTO);

            log.info("根据外委产品更新转外委数据成功，产品名称: {}, 外委产品: {}", itemDTO.getProductName(), itemDTO.getOutsourcingProduct());
            return Result.OK(updatedItem);

        } catch (Exception e) {
            log.error("根据外委产品更新转外委数据失败，产品名称: {}, 外委产品: {}", itemDTO.getProductName(), itemDTO.getOutsourcingProduct(), e);
            return Result.error("根据外委产品更新转外委数据失败: " + e.getMessage());
        }
    }
}
