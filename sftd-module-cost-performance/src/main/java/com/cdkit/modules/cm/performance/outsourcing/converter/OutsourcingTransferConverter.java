package com.cdkit.modules.cm.performance.outsourcing.converter;

import com.cdkit.modules.cm.api.outsourcing.dto.OutsourcingTransferItemDTO;
import com.cdkit.modules.cm.api.outsourcing.dto.OutsourcingTransferListDTO;
import com.cdkit.modules.cm.domain.outsourcing.entity.OutsourcingTransferEntity;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 转外委转换器
 * 处理领域实体和API DTO之间的转换
 * 
 * <AUTHOR>
 * @date 2025-08-13
 */
public class OutsourcingTransferConverter {

    /**
     * 转换转外委实体列表为DTO列表
     * 支持子母表结构和序号生成
     * 
     * @param entities 转外委实体列表
     * @return DTO列表
     */
    public static List<OutsourcingTransferItemDTO> toItemDTOList(List<OutsourcingTransferEntity> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return Collections.emptyList();
        }

        List<OutsourcingTransferItemDTO> dtoList = new ArrayList<>();
        AtomicInteger sequence = new AtomicInteger(1);

        for (OutsourcingTransferEntity entity : entities) {
            OutsourcingTransferItemDTO dto = toItemDTO(entity, sequence);
            dtoList.add(dto);
        }

        return dtoList;
    }

    /**
     * 转换单个转外委实体为DTO
     * 
     * @param entity 转外委实体
     * @param sequence 序号计数器
     * @return DTO
     */
    public static OutsourcingTransferItemDTO toItemDTO(OutsourcingTransferEntity entity, AtomicInteger sequence) {
        if (entity == null) {
            return null;
        }

        OutsourcingTransferItemDTO dto = new OutsourcingTransferItemDTO()
                .setSequence(sequence.getAndIncrement())
                .setProductName(entity.getProductName())
                .setOutsourcingProduct(entity.getOutsourcingProduct())
                .setEstimatedUsage(entity.getEstimatedUsage())
                .setFormulaName(entity.getFormulaName())
                .setFormulaCode(entity.getFormulaCode())
                .setTransferredAmount(entity.getTransferredAmount())
                .setAvailableAmount(entity.getAvailableAmount())
                .setOutsourcingAmount(entity.getOutsourcingAmount())
                .setIsProduct(entity.getIsProduct())
                .setParentProductName(entity.getParentProductName())
                .setRecipeRatio(entity.getRecipeRatio())
                .setExpanded(false) // 默认不展开
                .setDirectCostId(entity.getDirectCostId())
                .setMaterialCode(entity.getMaterialCode())
                .setMaterialName(entity.getMaterialName())
                .setItemType(entity.getItemType());

        // 转换子项目
        if (!CollectionUtils.isEmpty(entity.getChildren())) {
            List<OutsourcingTransferItemDTO> children = new ArrayList<>();
            for (OutsourcingTransferEntity childEntity : entity.getChildren()) {
                OutsourcingTransferItemDTO childDto = toItemDTO(childEntity, sequence);
                children.add(childDto);
            }
            dto.setChildren(children);
        }

        return dto;
    }

    /**
     * 转换DTO为转外委实体
     * 
     * @param dto DTO
     * @return 转外委实体
     */
    public static OutsourcingTransferEntity toEntity(OutsourcingTransferItemDTO dto) {
        if (dto == null) {
            return null;
        }

        OutsourcingTransferEntity entity = new OutsourcingTransferEntity()
                .setProductName(dto.getProductName())
                .setOutsourcingProduct(dto.getOutsourcingProduct())
                .setEstimatedUsage(dto.getEstimatedUsage())
                .setFormulaName(dto.getFormulaName())
                .setFormulaCode(dto.getFormulaCode())
                .setTransferredAmount(dto.getTransferredAmount())
                .setAvailableAmount(dto.getAvailableAmount())
                .setOutsourcingAmount(dto.getOutsourcingAmount())
                .setIsProduct(dto.getIsProduct())
                .setParentProductName(dto.getParentProductName())
                .setRecipeRatio(dto.getRecipeRatio())
                .setDirectCostId(dto.getDirectCostId())
                .setMaterialCode(dto.getMaterialCode())
                .setMaterialName(dto.getMaterialName())
                .setItemType(dto.getItemType());

        // 转换子项目
        if (!CollectionUtils.isEmpty(dto.getChildren())) {
            List<OutsourcingTransferEntity> children = new ArrayList<>();
            for (OutsourcingTransferItemDTO childDto : dto.getChildren()) {
                OutsourcingTransferEntity childEntity = toEntity(childDto);
                children.add(childEntity);
            }
            entity.setChildren(children);
        }

        return entity;
    }

    /**
     * 转换DTO列表为转外委实体列表
     * 
     * @param dtoList DTO列表
     * @return 转外委实体列表
     */
    public static List<OutsourcingTransferEntity> toEntityList(List<OutsourcingTransferItemDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return Collections.emptyList();
        }

        List<OutsourcingTransferEntity> entities = new ArrayList<>();
        for (OutsourcingTransferItemDTO dto : dtoList) {
            OutsourcingTransferEntity entity = toEntity(dto);
            if (entity != null) {
                entities.add(entity);
            }
        }

        return entities;
    }

    /**
     * 创建转外委列表响应DTO
     * 只返回转外委项目明细列表
     *
     * @param entities 转外委实体列表
     * @return 转外委列表响应DTO
     */
    public static OutsourcingTransferListDTO createListDTO(List<OutsourcingTransferEntity> entities) {
        List<OutsourcingTransferItemDTO> items = toItemDTOList(entities);
        return new OutsourcingTransferListDTO().setItems(items);
    }

    /**
     * 重新计算序号
     * 为子母表结构重新分配连续的序号
     * 
     * @param items DTO列表
     */
    public static void recalculateSequence(List<OutsourcingTransferItemDTO> items) {
        if (CollectionUtils.isEmpty(items)) {
            return;
        }

        AtomicInteger sequence = new AtomicInteger(1);
        for (OutsourcingTransferItemDTO item : items) {
            item.setSequence(sequence.getAndIncrement());
            
            // 处理子项目序号
            if (!CollectionUtils.isEmpty(item.getChildren())) {
                for (OutsourcingTransferItemDTO child : item.getChildren()) {
                    child.setSequence(sequence.getAndIncrement());
                }
            }
        }
    }

    /**
     * 计算总记录数
     * 包括产品和半成品的总数量
     * 
     * @param items DTO列表
     * @return 总记录数
     */
    public static int calculateTotalCount(List<OutsourcingTransferItemDTO> items) {
        if (CollectionUtils.isEmpty(items)) {
            return 0;
        }

        int count = 0;
        for (OutsourcingTransferItemDTO item : items) {
            count++; // 产品本身
            
            // 加上子项目数量
            if (!CollectionUtils.isEmpty(item.getChildren())) {
                count += item.getChildren().size();
            }
        }

        return count;
    }

    /**
     * 扁平化子母表结构
     * 将子母表结构转换为扁平列表，便于前端表格显示
     * 
     * @param items 子母表结构的DTO列表
     * @return 扁平化的DTO列表
     */
    public static List<OutsourcingTransferItemDTO> flattenItems(List<OutsourcingTransferItemDTO> items) {
        if (CollectionUtils.isEmpty(items)) {
            return Collections.emptyList();
        }

        List<OutsourcingTransferItemDTO> flatList = new ArrayList<>();
        AtomicInteger sequence = new AtomicInteger(1);

        for (OutsourcingTransferItemDTO item : items) {
            // 添加产品
            item.setSequence(sequence.getAndIncrement());
            flatList.add(item);
            
            // 添加子项目（半成品）
            if (!CollectionUtils.isEmpty(item.getChildren())) {
                for (OutsourcingTransferItemDTO child : item.getChildren()) {
                    child.setSequence(sequence.getAndIncrement());
                    flatList.add(child);
                }
                // 清空children，避免重复
                item.setChildren(null);
            }
        }

        return flatList;
    }

    /**
     * 构建子母表结构
     * 将扁平列表重新构建为子母表结构
     * 
     * @param flatItems 扁平化的DTO列表
     * @return 子母表结构的DTO列表
     */
    public static List<OutsourcingTransferItemDTO> buildHierarchy(List<OutsourcingTransferItemDTO> flatItems) {
        if (CollectionUtils.isEmpty(flatItems)) {
            return Collections.emptyList();
        }

        List<OutsourcingTransferItemDTO> hierarchyItems = new ArrayList<>();
        OutsourcingTransferItemDTO currentProduct = null;

        for (OutsourcingTransferItemDTO item : flatItems) {
            if (Boolean.TRUE.equals(item.getIsProduct())) {
                // 产品项目
                if (currentProduct != null) {
                    hierarchyItems.add(currentProduct);
                }
                currentProduct = item;
                currentProduct.setChildren(new ArrayList<>());
            } else {
                // 半成品项目
                if (currentProduct != null) {
                    currentProduct.getChildren().add(item);
                }
            }
        }

        // 添加最后一个产品
        if (currentProduct != null) {
            hierarchyItems.add(currentProduct);
        }

        return hierarchyItems;
    }
}
